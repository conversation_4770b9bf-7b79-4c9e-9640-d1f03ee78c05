// models/Notification.js
const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema({
    // Notification content
    type: {
        type: String,
        required: true,
        enum: ['system', 'message', 'comment', 'like', 'mention', 'alert', 'other'],
        index: true
    },
    targetType: {
        type: String,
        enum: ['user', 'all', 'role', 'users'],
        default: 'user'
    },
    title: {
        type: String,
        required: true,
        trim: true,
    },
    message: {
        type: String,
        required: true,
        trim: true,
    },

    // Target information
    sender: {
        type: String,
        index: true
    },
    recipients: [{
        user: {
            type: String,
            required: true
        },
        read: {
            type: Boolean,
            default: false
        },
        readAt: {
            type: Date
        },
        _id: false
    }],

    // Optional reference to related entity
    relatedEntity: {
        type: {
            type: String,
            enum: ['Post', 'Comment', 'Message', 'User', 'Product', null],
            default: null
        },
        id: {
            type: String,
            default: null
        }
    },

    // Metadata
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
    },
    data: {
        type: Object,
        default: {}
    },

    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    expiresAt: {
        type: Date,
        index: true,
        expires: 0 // For automatic deletion after expiration
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

const Notification = mongoose.model('Notification', NotificationSchema);
module.exports = Notification