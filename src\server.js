const cors = require ('cors')
const cookieParser = require('cookie-parser');
const dotenv = require ('dotenv')
const logger = require('@aplicy-com/gigmosaic-common-libs');
const { app, server } = require('./socket/socket');
const { connectToDatabase } = require('./config/db');
const router = require('./modules/webNotification/router');
const YAML = require('yamljs');
const swaggerUi = require('swagger-ui-express');
const path = require('path');

dotenv.config();
app.use(cookieParser());

const swaggerDocument = YAML.load(path.join(__dirname, 'OpenAPI.yaml'));

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

app.use(
    cors({
        origin: [
            process.env.FRONTEND_DOMAIN,
            process.env.ADMINFRONTEND_DOMAIN,
            process.env.PROVIDERFRONTEND_DOMAIN,
        ],
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
        credentials: true,
    })
);
connectToDatabase()

app.use((req, res, next) => {
    logger.info(`Incoming ${req.method} ${req.url}`);
    next();
});

app.get('/test', (req, res) => {
    res.send('Hello World!');
});

app.use('/api',router)

server.listen(process.env.PORT, () => {
    logger.info(`Server is running on port ${process.env.PORT}`);
});