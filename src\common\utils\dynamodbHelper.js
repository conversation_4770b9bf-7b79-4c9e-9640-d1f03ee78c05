const dotenv = require('dotenv');
dotenv.config();


async function getAllConnections() {
  const result = await dynamoDb.scan({ TableName: process.env.CONNECTIONS_TABLE });
  return result.Items.map(i => AWS.DynamoDB.Converter.unmarshall(i));
}

async function getConnectionsByUserIds(userIds) {
  // Assuming you store userId in your connection table
  const results = [];
  for (const userId of userIds) {
    const res = await dynamoDb.query({
      TableName: process.env.CONNECTIONS_TABLE,
      IndexName: 'UserIdIndex', // GSI on userId
      KeyConditionExpression: 'userId = :u',
      ExpressionAttributeValues: { ':u': { S: userId } }
    });
    results.push(...res.Items.map(i => AWS.DynamoDB.Converter.unmarshall(i)));
  }
  return results;
}

async function getConnectionsByRoles(roles) {
  // If you store roles array in each record, you may need a scan + filter
  const result = await dynamoDb.scan({ TableName: process.env.CONNECTIONS_TABLE });
  return result.Items
    .map(i => AWS.DynamoDB.Converter.unmarshall(i))
    .filter(conn => conn.roles && conn.roles.some(r => roles.includes(r)));
}

async function removeConnection(connectionId) {
  await dynamoDb.deleteItem({
    TableName: process.env.CONNECTIONS_TABLE,
    Key: { connectionId: { S: connectionId } },
  });
}

module.exports = {
    getAllConnections,
    getConnectionsByUserIds,
    getConnectionsByRoles,
    removeConnection
}