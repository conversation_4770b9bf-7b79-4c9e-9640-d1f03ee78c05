const Notification = require("./webNotifictionModel")
const errorUtils = require('../../common/utils/error');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;

const createNotification = async ({
    type,
    targetType,
    title,
    message,
    sender,
    recipients,
    relatedEntity,
    priority = 'medium',
    data = {},
    expiresAt
}) => {
    try {
        const notification = await Notification.create({
            type,
            targetType,
            title,
            message,
            sender,
            recipients: recipients.map(recipient => ({
                user: recipient,
                read: false
            })),
            relatedEntity,
            priority,
            data,
            expiresAt
        });

        return notification;
    } catch (error) {
        error.errorId = error.generateErrorId();
        throw error;
    }
};

const getNotification = async ({
    userId,
    limit,
    page
}) => {
    try {
        if (!userId || userId === 'undefined') {
            return {
                data: [],
                pagination: {
                    total: 0,
                    page,
                    limit,
                    totalPages: 0
                }
            };
        }
        const skip = (page - 1) * limit;

        const query = {
            'recipients.user': userId,
        };

        const [notifications, total] = await Promise.all([
            Notification.find({
                $and: [
                    {
                        $or: [
                            { targetType: 'user', 'recipients.user': userId },
                            { targetType: 'users', 'recipients.user': userId },
                            { targetType: 'all' },
                            // { targetType: 'role', 'recipients.user': userId, 'recipients.read': false },

                        ]
                    }
                ]
            })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean(),
            Notification.countDocuments(query)
        ]);

        const processed = notifications.map(notification => {
            const recipient = notification.recipients.find(r => r.user === userId);
            return {
                ...notification,
                read: recipient?.read ?? false
            };
        });

        return {
            data: processed,
            pagination: {
                total: total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            }
        };
    } catch (error) {
        error.errorId = errorUtils.generateErrorId();
        throw error;
    }
};

const getUnreadNotification = async ({
    userId,
    role
}) => {
    console.log("userId",userId);
    if (!userId || userId === 'undefined') {
        console.log('Invalid userId:', userId);
        return [];

    }
    const unreadNotification = await Notification.find({
        $or: [
            // Direct user-targeted unread
            { targetType: 'user', recipients: { $elemMatch: { user: userId, read: false } } },
            { targetType: 'users', recipients: { $elemMatch: { user: userId, read: false } } },

            // Global (targetType: all)
            {
                targetType: 'all',
                $or: [
                    // No recipients array at all or empty
                    { recipients: { $exists: false } },
                    { recipients: { $size: 0 } },

                    // User not present in recipients at all
                    { recipients: { $not: { $elemMatch: { user: userId } } } },

                    // User present but unread
                    { recipients: { $elemMatch: { user: userId, read: false } } }
                ]
            }
        ]
    }).sort({ createdAt: -1 });

    console.log('Unread notifications:', unreadNotification);
    return unreadNotification.length
}

const markReadNotification = async ({ userId, notificationId }) => {
     let ids = [];

    // Normalize notificationId input
    if (Array.isArray(notificationId)) {
        ids = notificationId.map(id => {
            if (typeof id === 'object' && id.notificationId) {
                return id.notificationId;
            }
            return id;
        });
    } else if (typeof notificationId === 'object' && notificationId.notificationId) {
        ids = [notificationId.notificationId];
    } else {
        ids = [notificationId];
    }

     try {
        ids = ids.map(id => new ObjectId(id));
    } catch (err) {
        console.error('Invalid notificationId:', ids, err);
        throw new Error('One or more notification IDs are invalid.');
    }

    const result = await Notification.updateMany(
        {
            _id: { $in: ids }
        },
        [
            {
                $set: {
                    recipients: {
                        $cond: {
                            if: { $in: [userId, "$recipients.user"] },
                            then: {
                                $map: {
                                    input: "$recipients",
                                    as: "recipient",
                                    in: {
                                        $cond: {
                                            if: { $eq: ["$$recipient.user", userId] },
                                            then: {
                                                user: userId,
                                                read: true,
                                                readAt: new Date()
                                            },
                                            else: "$$recipient"
                                        }
                                    }
                                }
                            },
                            else: {
                                $concatArrays: [
                                    "$recipients",
                                    [{
                                        user: userId,
                                        read: true,
                                        readAt: new Date()
                                    }]
                                ]
                            }
                        }
                    }
                }
            }
        ]
    );

    console.log('Mark read result:', result);
    return result;
};


module.exports = {
    createNotification,
    getNotification,
    getUnreadNotification,
    markReadNotification
}