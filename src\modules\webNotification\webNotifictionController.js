const { validationResult } = require('express-validator');
const errorUtils = require('../../common/utils/error');
const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');
const logger = require('@aplicy-com/gigmosaic-common-libs');
const NotificationService = require('./webNotifictionService');
const  deliverToConnectedClients  = require('../../common/utils/deliverToConnectedClients');

const createNotification = async (req, res) => {
    try {
        const {
            type,
            targetType,
            title,
            message,
            sender,
            recipients,
            relatedEntity,
            priority,
            data,
            expiresAt
        } = req.body;

        if (!type || !title || !message || !recipients || !Array.isArray(recipients)) {
            const errorId = errorUtils.generateErrorId();
            const errorResponse = errorUtils.createErrorResponse(
                [{ field: 'validation', message: 'Missing required fields or invalid recipients format' }],
                errorUtils.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        const notification = await NotificationService.createNotification({
            type,
            targetType,
            title,
            message,
            sender,
            recipients,
            relatedEntity,
            priority,
            data,
            expiresAt
        });
        deliverToConnectedClients(notification);
        return res.status(201).json(notification);
    } catch (err) {
        const errorId = err.errorId || errorUtils.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `createNotification Insufficient scope error. Message: ${err.message}`,
                { errorId }
            );

            const errorResponse = errorUtils.createErrorResponse(
                [{ field: 'createNotification scope', message: err.message }],
                errorUtils.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating Notification: ${err.message}`, {
            errorId,
            stack: err.stack
        });

        const errorResponse = errorUtils.createErrorResponse(
            [{ field: 'notification', message: err.message }],
            errorUtils.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const getNotification = async (req, res) => {
    try {
        const { userId } = req.params
        const { type, limit = 20, page = 1 } = req.query

        const notification = await NotificationService.getNotification({
            userId, limit, page
        });

        return res.status(200).json(notification);
    } catch (err) {
        const errorId = err.errorId || errorUtils.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `getNotification Insufficient scope error. Message: ${err.message}`,
                { errorId }
            );

            const errorResponse = errorUtils.createErrorResponse(
                [{ field: 'getNotification scope', message: err.message }],
                errorUtils.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error get Notification: ${err.message}`, {
            errorId,
            stack: err.stack
        });

        const errorResponse = errorUtils.createErrorResponse(
            [{ field: 'notification', message: err.message }],
            errorUtils.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const getNotifications = async (req, res) => {
    try {
        const { userId } = req.params

        const notification = await NotificationService.getUnreadNotification({
            userId
        });

        return res.status(200).json(notification);
    } catch (err) {
        const errorId = err.errorId || errorUtils.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `getNotificationS Insufficient scope error. Message: ${err.message}`,
                { errorId }
            );

            const errorResponse = errorUtils.createErrorResponse(
                [{ field: 'getNotificationS scope', message: err.message }],
                errorUtils.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error get NotificationS: ${err.message}`, {
            errorId,
            stack: err.stack
        });

        const errorResponse = errorUtils.createErrorResponse(
            [{ field: 'notification', message: err.message }],
            errorUtils.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const MarkReadNotification = async (req, res) => {
    try {
        const { userId } = req.params
        const notificationId = req.body

        const notification = await NotificationService.markReadNotification({
            userId, notificationId
        });

        return res.status(200).json(notification);
    } catch (err) {
        const errorId = err.errorId || errorUtils.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `markReadNotification Insufficient scope error. Message: ${err.message}`,
                { errorId }
            );

            const errorResponse = errorUtils.createErrorResponse(
                [{ field: 'markReadNotification scope', message: err.message }],
                errorUtils.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error markReadNotification: ${err.message}`, {
            errorId,
            stack: err.stack
        });

        const errorResponse = errorUtils.createErrorResponse(
            [{ field: 'notification', message: err.message }],
            errorUtils.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createNotification,
    getNotification,
    MarkReadNotification,
    getNotifications
}