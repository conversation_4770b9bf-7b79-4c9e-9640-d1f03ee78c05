// const { app } = require("../../socket/socket");

// function deliverToConnectedClients(notif) {
//   const clients = app.get('wssClients'); // or require a shared module
//   const payload = { type: 'notification', data: notif };

//   if (notif.targetType === 'all') {
//     for (const [userId, { ws }] of clients) {
//       if (ws.readyState === ws.OPEN) ws.send(JSON.stringify(payload));
//     }
//     return;
//   }

//   if (notif.targetType === 'role') {
//     for (const [userId, { ws, roles }] of clients) {
//       if (ws.readyState !== ws.OPEN) continue;
//       // if the client's roles intersect the notif.roles
//       if (roles && roles.some(r => notif.roles.includes(r))) {
//         ws.send(JSON.stringify(payload));
//       }
//     }
//     return;
//   }

//   if (notif.targetType === 'users') {
//     const targetSet = new Set(notif.targets.map(id => String(id)));
//     for (const [userId, { ws }] of clients) {
//       if (ws.readyState !== ws.OPEN) continue;
//       if (targetSet.has(String(userId))) ws.send(JSON.stringify(payload));
//     }
//     return;
//   }

//   // single-user (user field)
//   if (notif.targetType === 'user' && notif.user) {
//     const entry = clients.get(String(notif.user));
//     if (entry && entry.ws.readyState === entry.ws.OPEN) {
//       entry.ws.send(JSON.stringify(payload));
//     }
//   }
// }


const AWS = require('aws-sdk');
const { DynamoDB } = require('@aws-sdk/client-dynamodb');
const dotenv = require('dotenv');
const { getAllConnections, getConnectionsByRoles, getConnectionsByUserIds, removeConnection } = require('./dynamodbHelper');
dotenv.config();

const dynamoDb = new DynamoDB({ region: process.env.AWS_REGION });

async function deliverToConnectedClients(notif) {
  const apigw = new AWS.ApiGatewayManagementApi({
    apiVersion: '2018-11-29',
    endpoint: process.env.WS_API_ENDPOINT, // e.g. wss://xxxx.execute-api.us-east-1.amazonaws.com/dev
  });

  const payload = { type: 'notification', data: notif };

  // Fetch all connections from DynamoDB (or filter by user/role if you store that info)
  let connections;
  if (notif.targetType === 'all') {
    connections = await getAllConnections();
  } else if (notif.targetType === 'role') {
    connections = await getConnectionsByRoles(notif.roles);
  } else if (notif.targetType === 'users') {
    connections = await getConnectionsByUserIds(notif.targets);
  } else if (notif.targetType === 'user' && notif.user) {
    connections = await getConnectionsByUserIds([notif.user]);
  }

  // Send the message to each connection
  for (const conn of connections) {
    try {
      await apigw.postToConnection({
        ConnectionId: conn.connectionId,
        Data: JSON.stringify(payload),
      }).promise();
    } catch (err) {
      if (err.statusCode === 410) {
        // stale connection → remove from DB
        await removeConnection(conn.connectionId);
      } else {
        console.error("Failed to send", err);
      }
    }
  }
}


module.exports= deliverToConnectedClients