const express = require('express');

const webNotifictionController = require('./webNotifictionController');

const router = express.Router();

router.post('/', webNotifictionController.createNotification);
router.get('/:userId', webNotifictionController.getNotification);
router.get('/getunread/:userId', webNotifictionController.getNotifications);
router.patch('/:userId', webNotifictionController.MarkReadNotification);

module.exports = router;