info:
  title: notificationService
  description: ''
  version: 1.0.0
tags: []
paths:
  /:
    post:
      summary: create notification
      deprecated: false
      description: ''
      tags: []
      parameters:
        - name: body
          in: body
          schema:
            type: object
            properties:
              type:
                type: string
              targetType:
                type: string
              title:
                type: string
              message:
                type: string
              sender:
                type: string
              recipients:
                type: array
                items: {}
              priority:
                type: string
              data:
                type: object
                properties: {}
            required:
              - type
              - targetType
              - title
              - message
              - sender
              - recipients
              - priority
              - data
      responses:
        '200':
          description: ''
          headers: {}
          schema:
            type: object
            properties: {}
      security: []
      consumes:
        - application/json
      produces:
        - application/json
  /{userId}:
    get:
      summary: getNotifications
      deprecated: false
      description: ''
      tags: []
      parameters:
        - name: userId
          in: path
          description: ''
          required: true
          type: string
          x-example: ''
      responses:
        '200':
          description: ''
          headers: {}
          schema:
            type: object
            properties: {}
      security: []
      produces:
        - application/json
    patch:
      summary: markAsRead notification
      deprecated: false
      description: ''
      tags: []
      parameters:
        - name: userId
          in: path
          description: ''
          required: true
          type: string
          x-example: ''
      responses:
        '200':
          description: ''
          headers: {}
          schema:
            type: object
            properties: {}
      security: []
      produces:
        - application/json
security: []
swagger: '2.0'
definitions: {}
securityDefinitions: {}
x-components: {}
