# 🎉 Gigmosaic Backend


This repository contains the backend implementation for the gigmosaic-be-notification-service. It is built using Node.js, Express, and Mongodb, and it handles events management with user permissions.

## 📚 Table of Contents

- [🎉 gigmosaic-be-notification-service Backend](#-gigmosaic-be-notification-service-backend)
    - [📚 Table of Contents](#-table-of-contents)
    - [🚀 Getting Started](#-getting-started)
        - [⚙️ Installation](#️-installation)
    - [🛠️ Usage](#️-usage)
        - [Running in Development](#running-in-development)
        - [Building for Production](#building-for-production)
    - [📡 API Endpoints](#-api-endpoints)
        - [NotificationEndpoints](#NotificationEndpoints)
    - [📁 Project Structure](#-project-structure)
    - [🔧 Environment Variables](#-environment-variables)
    - [📜 License](#-license)

## 🚀 Getting Started

Follow the instructions below to get the project up and running on your local machine.

###📋 Prerequisites
Ensure you have the following installed:

- 🌐 Node.js (v20.x or later)
- 📦 npm (v6.x or later) or Yarn (v1.x or later)
- 🗄️ Mongodb

### ⚙️ Installation

1. Clone the repository:

    ```bash
    git clone https://github.com/your-username/gigmosaic-be-notification-service.git
    cd gigmosaic-be-notification-service
    ```

2. Install the dependencies:

    ```bash
    npm install
    # or
    yarn install
    ```

3. Set up the environment variables:

    Create a `.env` file in the root directory and add the necessary environment variables as shown in the [Environment Variables](#environment-variables) section.

4. Start the development server:
    
    ```bash
    RUN npx npmrc-replace-env
    ```

    ```bash
    npm run dev
    # or
    yarn dev
    ```

## 🛠️ Usage

### Running in Development

To run the project in development mode with hot reloading:

```bash
npm run dev
# or
yarn dev
```

### Building for Production

To build the project for production:

```bash
npm run build
# or
yarn build
```

To start the production server:

```bash
npm start
# or
yarn start
```

## 📡 API Endpoints

### NotificationEndpoints

- \*_createNotification_

    ```http
    POST /
    ```

- **Get getNotification**

    ```http
    GET /:userId
    ```

- \*_MarkReadNotification_

    ```http
    PATCH /:userId
    ```

## 📁 Project Structure

```plaintext
gigmosaic-be-notification-service/

gigmosaic-be-notification-service/
├── src/
│   ├── common/
│   │   └── utils/
│   ├── config/
│   ├── modules
|   |   ├── webNotification/
│   ├── socket      
│   ├── OpenApi.yml
│   └── server.ts
├── .env
├── package.json
└── README.md


```

## 🔧 Environment Variables

The project uses the following environment variables:

```plaintext
#local
FRONTEND_DOMAIN=http://localhost:3000
PROVIDERFRONTEND_DOMAIN=http://localhost:3000
ADMINFRONTEND_DOMAIN=http://localhost:3000
NODE_ENV=development
PORT=3050
MONGODB_URL=mongodb://localhost:27017/gigmosaic-be-notification-service
NPMRC_MYTOKEN1=github access token


```

Ensure these are set in your `.env` file before starting the project.


## 📜 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
