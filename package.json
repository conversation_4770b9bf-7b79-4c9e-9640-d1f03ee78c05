{"name": "gigmosaic-notification-service", "version": "1.0.5", "main": "src/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@aplicy-com/gigmosaic-common-libs": "^1.0.3", "@aws-sdk/client-dynamodb": "^3.879.0", "aws-sdk": "^2.1692.0", "axios": "^1.11.0", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-oauth2-jwt-bearer": "^1.6.1", "express-validator": "^7.2.1", "http": "^0.0.1-security", "mongoose": "^8.17.1", "nodemon": "^3.1.10", "path": "^0.12.7", "prettier": "^3.6.2", "swagger-ui-express": "^5.0.1", "ws": "^8.18.3", "yamljs": "^0.3.0"}}