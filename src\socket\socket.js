const WebSocket = require('ws');
const http = require('http');
const express = require('express');
const logger = require('@aplicy-com/gigmosaic-common-libs');
const { getUnreadNotification, markReadNotification } = require('../modules/webNotification/webNotifictionService');

const app = express()
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true })); 

const server = http.createServer(app);
const wss = new WebSocket.Server({ server });
const clients = new Map();
app.set('wssClients', clients);


wss.on('connection', async (ws, req) => {
    let userId
    try {
        const queryParams = new URLSearchParams(req.url.split('?')[1])
        userId = queryParams.get('userId')
        console.log("Connected userId:", userId);

        
    } catch (error) {
        ws.close(4001, 'Invalid URL format')
        return;
    }

    if (!userId || userId === 'undefined') {
        ws.close(4001,'userId Missing')
        return;
    }
    clients.set(String(userId), { ws });
    const unreadNotification = await getUnreadNotification({userId})
    ws.send(JSON.stringify({ type: 'notification', data: unreadNotification }));

    ws.on('message', async (message)=>{
        try {
           const parsed = JSON.parse(message.toString());
            const { event, data } = parsed;
            logger.info(`Received event: ${event} with data:`, data);
            switch (event) {
                case 'markRead':
                    await markReadNotification({ userId: data.userId, notificationId: data.NotificationId });
                    break;
            
                default:
                    break;
            }
        } catch (error) {
            
        }
    })
})

module.exports = { app, wss, server };
